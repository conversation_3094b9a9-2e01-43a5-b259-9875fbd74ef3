/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AddressBook: typeof import('./src/platform/dataManage/deviceManage/common/addressBook.vue')['default']
    AddressBookGroup: typeof import('./src/platform/dataManage/deviceManage/common/addressBookGroup.vue')['default']
    AdminLayout: typeof import('./src/layouts/AdminLayout.vue')['default']
    AlarmHistory: typeof import('./src/components/historyDialog/alarmHistory.vue')['default']
    App: typeof import('./src/App.vue')['default']
    AuthAppMapPrivilegeDevice: typeof import('./src/components/command/authAppMapPrivilegeDevice.vue')['default']
    Authorization: typeof import('./src/platform/dataManage/otherManage/authorization.vue')['default']
    BaseMap: typeof import('./src/components/common/BaseMap.vue')['default']
    BcxxResult: typeof import('./src/components/secondary/bcxxResult.vue')['default']
    BfDataNav: typeof import('./src/layouts/BfDataNav.vue')['default']
    BfHead: typeof import('./src/components/layout/BfHead.vue')['default']
    BFHead: typeof import('./src/layouts/BFHead.vue')['default']
    BfInputNumber: typeof import('./src/components/common/bfInputNumber/bf-input-number.vue')['default']
    BfLogin: typeof import('./src/layouts/BfLogin.vue')['default']
    BfMain: typeof import('./src/components/layout/BfMain.vue')['default']
    BfMap: typeof import('./src/components/layout/BfMap.vue')['default']
    BfSpeaking: typeof import('./src/components/command/bfSpeaking.vue')['default']
    BfTabsContainer: typeof import('./src/components/bfTabsContainer.vue')['default']
    BfTree: typeof import('./src/components/layout/BfTree.vue')['default']
    BP610: typeof import('./src/platform/dataManage/deviceManage/views/BP610.vue')['default']
    BP620: typeof import('./src/platform/dataManage/deviceManage/views/BP620.vue')['default']
    BP660: typeof import('./src/platform/dataManage/deviceManage/views/BP660.vue')['default']
    BP750SDC: typeof import('./src/platform/dataManage/deviceManage/views/BP750SDC.vue')['default']
    BP750SVT: typeof import('./src/platform/dataManage/deviceManage/views/BP750SVT.vue')['default']
    BP860SDC: typeof import('./src/platform/dataManage/deviceManage/views/BP860SDC.vue')['default']
    BP860SVT: typeof import('./src/platform/dataManage/deviceManage/views/BP860SVT.vue')['default']
    Channel: typeof import('./src/platform/dataManage/controllerManage/tr925/Channel.vue')['default']
    Channel_v9: typeof import('./src/platform/dataManage/deviceManage/common/channel/channel_v9.vue')['default']
    ChannelSettings: typeof import('./src/components/common/ChannelSettings.vue')['default']
    ChannelTransfer: typeof import('./src/platform/dataManage/deviceManage/common/channelTransfer.vue')['default']
    ControllerGateway: typeof import('./src/components/common/controllerGateway.vue')['default']
    Controllers: typeof import('./src/platform/dataManage/controllerManage/Controllers.vue')['default']
    CrudHistory: typeof import('./src/platform/dataManage/otherManage/crudHistory.vue')['default']
    CtrlonlineHistory: typeof import('./src/platform/dataManage/controllerManage/ctrlonlineHistory.vue')['default']
    CustomImage: typeof import('./src/components/common/customImage.vue')['default']
    DataFormEditor: typeof import('./src/components/common/DataFormEditor.vue')['default']
    DataStatusPopover: typeof import('./src/components/common/dataStatusPopover.vue')['default']
    DataTableElDatePicker: typeof import('./src/components/common/dataTable/DataTableElDatePicker.vue')['default']
    DataTableElSelect: typeof import('./src/components/common/dataTable/DataTableElSelect.vue')['default']
    DataTableIcon: typeof import('./src/components/common/dataTable/DataTableIcon.vue')['default']
    DataTableRow: typeof import('./src/components/common/dataTable/DataTableRow.vue')['default']
    DataTableRowInput: typeof import('./src/components/common/dataTable/DataTableRowInput.vue')['default']
    DataTableRowItem: typeof import('./src/components/common/dataTable/DataTableRowItem.vue')['default']
    DataTablesVue3: typeof import('./src/components/common/dataTablesVue3.vue')['default']
    DeviceInfo: typeof import('./src/platform/dataManage/deviceManage/common/deviceInfo.vue')['default']
    Devices: typeof import('./src/platform/dataManage/deviceManage/Devices.vue')['default']
    DeviceStateTable: typeof import('./src/components/secondary/deviceStateTable.vue')['default']
    DigitalAlert: typeof import('./src/platform/dataManage/deviceManage/common/digitalAlert.vue')['default']
    DispatchHistory: typeof import('./src/components/historyDialog/dispatchHistory.vue')['default']
    DmridInput: typeof import('./src/components/common/DmridInput.vue')['default']
    DynamicGroup: typeof import('./src/platform/dispatch/DynamicGroup.vue')['default']
    DynamicGroupMemberInfo: typeof import('./src/components/common/DynamicGroupMemberInfo.vue')['default']
    DynamicGroupTree: typeof import('./src/components/common/dynamicGroupTree/dynamic-group-tree.vue')['default']
    DZ1480: typeof import('./src/platform/dataManage/controllerManage/modelView/DZ1480.vue')['default']
    DZ1481: typeof import('./src/platform/dataManage/controllerManage/modelView/DZ1481.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    EllipsisText: typeof import('./src/components/common/EllipsisText.vue')['default']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMessageDetailContent: typeof import('./src/components/common/ElMessageDetailContent.vue')['default']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElOptionGroup: typeof import('element-plus/es')['ElOptionGroup']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTransfer: typeof import('element-plus/es')['ElTransfer']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    EmergencyAlarmConfig: typeof import('./src/platform/dataManage/deviceManage/common/emergencyAlarmConfig.vue')['default']
    EmergencyDialog: typeof import('./src/components/secondary/emergencyDialog.vue')['default']
    EncryptSettings: typeof import('./src/platform/dataManage/deviceManage/common/encryptSettings.vue')['default']
    Error404: typeof import('./src/layouts/Error404.vue')['default']
    FreqMapOffset: typeof import('./src/platform/dataManage/deviceManage/common/freqMapOffset.vue')['default']
    FrequencyMhz: typeof import('./src/components/common/FrequencyMhz/FrequencyMhz.vue')['default']
    GatewayFilter: typeof import('./src/platform/dataManage/phoneManage/GatewayFilter.vue')['default']
    GatewayPermission: typeof import('./src/platform/dataManage/phoneManage/GatewayPermission.vue')['default']
    GenerateDmrId: typeof import('./src/components/common/generateDmrId/generateDmrId.vue')['default']
    GpstraceHistory: typeof import('./src/components/historyDialog/gpstraceHistory.vue')['default']
    HistoryCommon: typeof import('./src/components/common/historyCommon.vue')['default']
    InspectionHistory: typeof import('./src/components/historyDialog/InspectionHistory.vue')['default']
    InsRulesHistory: typeof import('./src/components/historyDialog/InsRulesHistory.vue')['default']
    InterphoneWf: typeof import('./src/platform/dataManage/deviceManage/interphoneWf.vue')['default']
    IOT: typeof import('./src/platform/dataManage/IOT.vue')['default']
    IotDeviceHistory: typeof import('./src/components/historyDialog/iotDeviceHistory.vue')['default']
    IpInput: typeof import('./src/components/common/IpInput/IpInput.vue')['default']
    Jobs: typeof import('./src/platform/dataManage/Jobs.vue')['default']
    Layout: typeof import('./src/layouts/Layout.vue')['default']
    LinePoint: typeof import('./src/platform/dataManage/patrolManage/LinePoint.vue')['default']
    Lines: typeof import('./src/platform/dataManage/patrolManage/Lines.vue')['default']
    LonLat: typeof import('./src/components/common/lonLat.vue')['default']
    LowBatteryAlarm: typeof import('./src/components/secondary/lowBatteryAlarm.vue')['default']
    MapPoints: typeof import('./src/platform/dataManage/otherManage/MapPoints.vue')['default']
    MenuBtn: typeof import('./src/components/common/MenuBtn.vue')['default']
    MultistageZone: typeof import('./src/platform/dataManage/deviceManage/common/multistageZone.vue')['default']
    NetworkSetting: typeof import('./src/platform/dataManage/controllerManage/common/NetworkSetting.vue')['default']
    Notes: typeof import('./src/platform/dataManage/otherManage/notes.vue')['default']
    OnlineHistory: typeof import('./src/platform/dataManage/deviceManage/onlineHistory.vue')['default']
    Orgs: typeof import('./src/platform/dataManage/Orgs.vue')['default']
    PageHeader: typeof import('./src/layouts/PageHeader.vue')['default']
    PatrolConfig: typeof import('./src/platform/dataManage/deviceManage/common/patrolConfig.vue')['default']
    PhoneBook: typeof import('./src/platform/dataManage/deviceManage/common/phoneBook.vue')['default']
    PocSetting: typeof import('./src/components/common/PocSetting.vue')['default']
    PredefinedPhoneBook: typeof import('./src/platform/dataManage/phoneManage/PredefinedPhoneBook.vue')['default']
    PreSetChannel: typeof import('./src/platform/dataManage/deviceManage/common/preSetChannel.vue')['default']
    ProchatDeviceSelect: typeof import('./src/components/common/prochatDeviceSelect.vue')['default']
    ProchatGatewaySetting: typeof import('./src/components/common/prochatGatewaySetting.vue')['default']
    QuickRoutingTags: typeof import('./src/components/layout/QuickRoutingTags.vue')['default']
    ReceiveGroup: typeof import('./src/platform/dataManage/deviceManage/common/receiveGroup.vue')['default']
    RecordList: typeof import('./src/platform/dataManage/deviceManage/common/recordList.vue')['default']
    RecordListV2: typeof import('./src/platform/dataManage/deviceManage/common/recordListV2.vue')['default']
    RelatedSoftware: typeof import('./src/platform/dataManage/otherManage/relatedSoftware.vue')['default']
    RepeaterInfo: typeof import('./src/platform/dataManage/controllerManage/common/RepeaterInfo.vue')['default']
    RepeaterKey: typeof import('./src/platform/dataManage/controllerManage/common/RepeaterKey.vue')['default']
    RepeaterStatusMonitor: typeof import('./src/components/common/RepeaterStatusMonitor.vue')['default']
    RepeaterWriteFrequency: typeof import('./src/platform/dataManage/controllerManage/repeaterWriteFrequency.vue')['default']
    Restart: typeof import('./src/platform/dataManage/controllerManage/common/Restart.vue')['default']
    RfidBatteryAlarm: typeof import('./src/components/historyDialog/rfidBatteryAlarm.vue')['default']
    RoamGroup: typeof import('./src/platform/dataManage/deviceManage/common/roamGroup.vue')['default']
    RoamGroupList: typeof import('./src/platform/dataManage/deviceManage/common/roamGroupList.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Rules: typeof import('./src/platform/dataManage/patrolManage/Rules.vue')['default']
    ScanGroup: typeof import('./src/platform/dataManage/deviceManage/common/scanGroup.vue')['default']
    ScanGroupList: typeof import('./src/platform/dataManage/deviceManage/common/scanGroupList.vue')['default']
    ScrollingText: typeof import('./src/components/common/scrollingText.vue')['default']
    SelectDevice: typeof import('./src/platform/dataManage/deviceManage/common/selectDevice.vue')['default']
    SelectListenGroup: typeof import('./src/components/common/selectListenGroup.vue')['default']
    Sendcmd: typeof import('./src/components/command/sendcmd.vue')['default']
    ServerSetting: typeof import('./src/platform/dataManage/controllerManage/common/ServerSetting.vue')['default']
    ShiftHistory: typeof import('./src/components/historyDialog/shiftHistory.vue')['default']
    ShortMessage: typeof import('./src/platform/dataManage/deviceManage/common/shortMessage.vue')['default']
    ShortNumberMapping: typeof import('./src/platform/dataManage/phoneManage/ShortNumberMapping.vue')['default']
    SipGatewaySettings: typeof import('./src/components/common/sipGatewaySettings/sipGatewaySettings.vue')['default']
    SipServerGatewaySetting: typeof import('./src/components/common/sipServerGatewaySetting.vue')['default']
    SiteInfo: typeof import('./src/platform/dataManage/deviceManage/common/SiteInfo.vue')['default']
    SiteInfo_511svt: typeof import('./src/platform/dataManage/deviceManage/common/SiteInfo_511svt.vue')['default']
    SmsHistory: typeof import('./src/components/historyDialog/smsHistory.vue')['default']
    SoundHistory: typeof import('./src/components/historyDialog/soundHistory.vue')['default']
    SwitchChannel: typeof import('./src/platform/dataManage/controllerManage/common/SwitchChannel.vue')['default']
    SwitchLangs: typeof import('./src/components/common/switchLangs.vue')['default']
    SwitchTransmitPower: typeof import('./src/platform/dataManage/controllerManage/common/SwitchTransmitPower.vue')['default']
    SystemLog: typeof import('./src/platform/dataManage/otherManage/systemLog/systemLog.vue')['default']
    SystemVersion: typeof import('./src/platform/dataManage/otherManage/systemVersion.vue')['default']
    TableTree: typeof import('./src/components/common/tableTree/tableTree.vue')['default']
    TD510SDC: typeof import('./src/platform/dataManage/deviceManage/views/TD510SDC.vue')['default']
    TD510SVT: typeof import('./src/platform/dataManage/deviceManage/views/TD510SVT.vue')['default']
    TD511SDC: typeof import('./src/platform/dataManage/deviceManage/views/TD511SDC.vue')['default']
    TD511SDC_FR: typeof import('./src/platform/dataManage/deviceManage/views/TD511SDC_FR.vue')['default']
    TD511SVT: typeof import('./src/platform/dataManage/deviceManage/views/TD511SVT.vue')['default']
    TD800SDC: typeof import('./src/platform/dataManage/deviceManage/views/TD800SDC.vue')['default']
    TD818SDC: typeof import('./src/platform/dataManage/deviceManage/views/TD818SDC.vue')['default']
    TD818SVT: typeof import('./src/platform/dataManage/deviceManage/views/TD818SVT.vue')['default']
    TD880SDC: typeof import('./src/platform/dataManage/deviceManage/views/TD880SDC.vue')['default']
    TD880SVT: typeof import('./src/platform/dataManage/deviceManage/views/TD880SVT.vue')['default']
    TD910PSDC: typeof import('./src/platform/dataManage/deviceManage/views/TD910PSDC.vue')['default']
    TD910SDC: typeof import('./src/platform/dataManage/deviceManage/views/TD910SDC.vue')['default']
    TD920: typeof import('./src/platform/dataManage/deviceManage/views/TD920.vue')['default']
    TD930SDC: typeof import('./src/platform/dataManage/deviceManage/views/TD930SDC.vue')['default']
    TD930SDC_R7F: typeof import('./src/platform/dataManage/deviceManage/views/TD930SDC_R7F.vue')['default']
    TD930SVT: typeof import('./src/platform/dataManage/deviceManage/views/TD930SVT.vue')['default']
    TD930SVT_R7F: typeof import('./src/platform/dataManage/deviceManage/views/TD930SVT_R7F.vue')['default']
    Td930Timezone: typeof import('./src/components/common/timezone/td930-timezone.vue')['default']
    Timezone: typeof import('./src/components/common/timezone/timezone.vue')['default']
    TimeZoneV2: typeof import('./src/platform/dataManage/deviceManage/common/TimeZoneV2.vue')['default']
    TM8250SDC: typeof import('./src/platform/dataManage/deviceManage/views/TM8250SDC.vue')['default']
    TM8250SDC_FR: typeof import('./src/platform/dataManage/deviceManage/views/TM8250SDC_FR.vue')['default']
    TM8250SDC_R7F: typeof import('./src/platform/dataManage/deviceManage/views/TM8250SDC_R7F.vue')['default']
    TM8250SVT_R7F: typeof import('./src/platform/dataManage/deviceManage/views/TM8250SVT_R7F.vue')['default']
    TR092501: typeof import('./src/platform/dataManage/controllerManage/modelView/TR092501.vue')['default']
    TR805005: typeof import('./src/platform/dataManage/controllerManage/modelView/TR805005.vue')['default']
    TR805005Channel: typeof import('./src/platform/dataManage/controllerManage/common/TR805005Channel.vue')['default']
    TR900M: typeof import('./src/platform/dataManage/controllerManage/modelView/TR900M.vue')['default']
    TR925BdContact: typeof import('./src/platform/dataManage/controllerManage/common/TR925BdContact.vue')['default']
    TR925BdSetting: typeof import('./src/platform/dataManage/controllerManage/common/TR925BdSetting.vue')['default']
    TR925Channel: typeof import('./src/platform/dataManage/controllerManage/common/TR925Channel.vue')['default']
    TR925CommonSetting: typeof import('./src/platform/dataManage/controllerManage/common/TR925CommonSetting.vue')['default']
    TR925ContactGrouping: typeof import('./src/platform/dataManage/controllerManage/common/TR925ContactGrouping.vue')['default']
    TR925DmrContact: typeof import('./src/platform/dataManage/controllerManage/common/TR925DmrContact.vue')['default']
    TR925DmrSetting: typeof import('./src/platform/dataManage/controllerManage/common/TR925DmrSetting.vue')['default']
    TR925MenuSetting: typeof import('./src/platform/dataManage/controllerManage/common/TR925MenuSetting.vue')['default']
    TR925PositionSetting: typeof import('./src/platform/dataManage/controllerManage/common/TR925PositionSetting.vue')['default']
    TR925PreMadeSms: typeof import('./src/platform/dataManage/controllerManage/common/TR925PreMadeSms.vue')['default']
    TR925ZoneIdSetting: typeof import('./src/platform/dataManage/controllerManage/common/TR925ZoneIdSetting.vue')['default']
    TraceMap: typeof import('./src/components/common/traceMap.vue')['default']
    TrackCtrol: typeof import('./src/components/common/trackCtrol.vue')['default']
    TreeToolbar: typeof import('./src/components/common/tableTree/treeToolbar/treeToolbar.vue')['default']
    Upgrade: typeof import('./src/layouts/upgrade.vue')['default']
    UserPrivelege: typeof import('./src/components/UserPrivelege.vue')['default']
    Users: typeof import('./src/platform/dataManage/Users.vue')['default']
    UserSetting: typeof import('./src/components/dialogs/userSetting.vue')['default']
    VirtualCluster_930svt_v0: typeof import('./src/platform/dataManage/deviceManage/common/virtualCluster/virtualCluster_930svt_v0.vue')['default']
    VirtualCluster_v0: typeof import('./src/platform/dataManage/deviceManage/common/virtualCluster/virtualCluster_v0.vue')['default']
    WriteFreqFooter: typeof import('./src/platform/dataManage/deviceManage/common/writeFreqFooter.vue')['default']
    ZoneChannelTable: typeof import('./src/platform/dataManage/deviceManage/common/zoneChannelTable.vue')['default']
    ZoneLeafTable: typeof import('./src/platform/dataManage/deviceManage/common/zoneLeafTable.vue')['default']
  }
}
