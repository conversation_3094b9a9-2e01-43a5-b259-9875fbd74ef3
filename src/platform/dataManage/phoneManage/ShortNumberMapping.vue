<template>
  <data-form-editor
    ref="formEditor"
    class="page-short-number-mapping"
    :title="dlgTitle"
    :tableName="dataTable.name"
    :data="dataTable.body"
    :column="dthead"
    :getNewData="getNewData"
    :beforeAction="beforeAction"
    :getFormRef="() => $refs.elFormRef"
    @row-delete="onDelete"
    @row-update="onUpdate"
    @row-new="onNew"
  >
    <template #form="{ formData }">
      <el-form
        ref="elFormRef"
        :model="formData"
        :label-width="formLabelWidth"
        :rules="getShortNumberRules(formData)"
        :validate-on-rule-change="false"
        class="grid grid-cols-1"
      >
        <el-form-item :label="$t('dialog.shortNumber')" prop="shortNo">
          <BfInput v-model="formData.shortNo" :maxlength="8" class="h-[50px] w-full" />
        </el-form-item>
        <el-form-item :label="$t('dialog.groupCallMapping')" prop="refOrgId">
          <BfSelect
            v-model="formData.refOrgId"
            :placeholder="$t('dialog.select')"
            filterable
            clearable
            :disabled="refOrgIdIsDisable(formData)"
            :no-match-text="$t('dialog.noMatchText')"
            @change="clearValidate('elFormRef', ['refOrgId', 'refDevId'])"
            class="h-[50px] w-full"
          >
            <el-option-group v-for="group in refOrgIdList" :key="group.label" :label="group.label">
              <el-option v-for="(item, index) in group.options" :key="index" :label="item.label" :value="item.rid" />
            </el-option-group>
          </BfSelect>
        </el-form-item>
        <el-form-item :label="$t('dialog.singleCallMapping')" prop="refDevId">
          <BfSelect
            v-model="formData.refDevId"
            :placeholder="$t('dialog.select')"
            filterable
            clearable
            :disabled="refDevIdIsDisable(formData)"
            :no-match-text="$t('dialog.noMatchText')"
            @change="clearValidate('elFormRef', ['refOrgId', 'refDevId'])"
            class="h-[50px] w-full"
          >
            <el-option v-for="(item, index) in refDevIdList" :key="index" :label="item.label" :value="item.rid" />
          </BfSelect>
        </el-form-item>
        <el-form-item :label="$t('dialog.notes')" prop="note">
          <BfInput v-model="formData.note" type="textarea" resize="none" :rows="3" :maxlength="128" class="w-full" />
        </el-form-item>
      </el-form>
    </template>
  </data-form-editor>
</template>

<script>
  import bfproto from '@/modules/protocol'
  import dbCmd from '@/modules/protocol/db.pb.cmd'
  import bfutil, { getDbSubject } from '@/utils/bfutil'

  import bfNotify from '@/utils/notify'
  import bfTime from '@/utils/time'
  import validateRules from '@/utils/validateRules'
  import vueMixin from '@/utils/vueMixin'
  import { v1 as uuid } from 'uuid'
  import DataFormEditor from '@/components/common/DataFormEditor.vue'
  import { cloneDeep } from 'lodash'
  import BfInput from '@/components/bfInput/main'
  import BfSelect from '@/components/bfSelect/main'
  import { convertPxToRem, calcScaleSize } from '@/utils/setRem'

  const defaultData = {
    rid: '',
    orgId: '',
    shortNo: '',
    refOrgId: '',
    refDevId: '',
    note: '',
  }
  export default {
    mixins: [vueMixin],
    data() {
      return {
        compName: 'vshortNnumberMapping',
        dataTable: {
          body: bfutil.objToArray(bfglob.gshortNo.getAll()).sort((a, b) => {
            return bfutil.sortByProps(a, b, { noPermission: 'asc' })
          }),
          name: 'shortMumberMappingTable',
        },
        orgDataList: bfglob.gorgData.getList(),
        virOrgDataList: bfglob.gorgData.getList(1),
        deviceDataList: bfglob.gdevices.getList(),
      }
    },
    methods: {
      /**
       * 编辑数据前置执行方法，允许拒绝编辑或添加新数据
       * @param {number} status Add: 1, Edit: 2, Delete: 3
       * @param {Record<string, any>?} row
       * @returns {Promise<boolan>}
       */
      beforeAction(status, row) {
        if (bfutil.notEditDataPermission()) {
          return Promise.reject('No permission')
        }
        // 不能删除权限外的数据
        if (status === 3 && bfutil.notDeleteDataPermission(row.orgId)) {
          return Promise.reject('No permission')
        }
        //编辑操作,两个必选下拉框其中之一值为默认值时，替换为空字符串显示
        if (status === 2 && row.refOrgId === bfutil.DefOrgRid) {
          this.$refs.formEditor.formData.refOrgId = ''
        }
        if (status === 2 && row.refDevId === bfutil.DefOrgRid) {
          this.$refs.formEditor.formData.refDevId = ''
        }
        return Promise.resolve(true)
      },
      getShortNumberRules(data) {
        const refOrgIdRule = [validateRules.required('change')]
        const refDevIdRule = [validateRules.required('change')]

        return {
          shortNo: [validateRules.required(), validateRules.mustNumber()],
          refOrgId: data.refDevId && data.refDevId !== bfutil.DefOrgRid ? [] : refOrgIdRule,
          refDevId: data.refOrgId && data.refOrgId !== bfutil.DefOrgRid ? [] : refDevIdRule,
        }
      },
      refOrgIdIsDisable(data) {
        return !!data.refDevId && data.refDevId !== bfutil.DefOrgRid
      },
      refDevIdIsDisable(data) {
        return !!data.refOrgId && data.refOrgId !== bfutil.DefOrgRid
      },
      async onDelete(row) {
        await this.delete_phone_short_no_data(row, dbCmd.DB_PHONE_SHORT_NO_DELETE)
      },
      async onUpdate(row, done) {
        const isOk = await this.update_phone_short_no_data(row, dbCmd.DB_PHONE_SHORT_NO_UPDATE)
        if (!isOk) return
        done()
      },
      // addNewCb：存在这个回调函数则需要继续添加新的一行
      async onNew(row, done, addNewCb) {
        const isOk = await this.add_phone_short_no_data(row, dbCmd.DB_PHONE_SHORT_NO_INSERT)
        if (!isOk) return
        if (addNewCb) {
          const __data = this.getNewData()
          // 短号自增
          __data.shortNo = '' + bfutil.numberAutoIncrement(row.shortNo, 99999999)
          // 重置标签页数据
          bfutil.resetForm(this, 'elFormRef')
          addNewCb(__data)
          return
        }
        done()
      },
      // 返回一个新的默认参数对象
      getNewData() {
        return cloneDeep(defaultData)
      },
      // 数据功能操作
      add_phone_short_no_data(data, add_db_cmd) {
        const msgObj = {
          ...data,
          rid: uuid(),
          refOrgId: data.refOrgId || bfutil.DefOrgRid,
          refDevId: data.refDevId || bfutil.DefOrgRid,
          orgId: bfutil.getBaseDataOrgId() || data.orgId || bfutil.DefOrgRid,
          lastModifyTime: bfTime.nowUtcTime(),
          setting: '{}',
        }
        bfglob.console.warn('addDataToDatabase:', msgObj)

        return bfproto
          .sendMessage(add_db_cmd, msgObj, 'db_phone_short_no', getDbSubject())
          .then(rpc_cmd_obj => {
            bfglob.console.log('add db_phone_short_no res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.addSuccess'), 'success')
              bfglob.emit('add_global_db_phone_short_no', msgObj)

              // 短号自增
              defaultData.shortNo = '' + bfutil.numberAutoIncrement(msgObj.shortNo, 99999999)

              // 添加日志
              const note = this.$t('dialog.add') + msgObj.shortNo + this.$t('dialog.shortNumber')
              bfglob.emit('addnote', note)
            } else {
              if (rpc_cmd_obj.resInfo.includes('db_phone_short_no_short_no_key')) {
                //短号重复
                bfNotify.messageBox(this.$t('msgbox.shortNoRepeated'), 'warning')
              } else {
                this.showAddErrorMsg()
              }
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('add short number timeout:', err)
            this.showAddErrorMsg()
            return Promise.resolve(false)
          })
      },
      update_phone_short_no_data(data, up_db_cmd) {
        const msgObj = {
          ...data,
          orgId: data.orgId || bfutil.DefOrgRid,
          refOrgId: data.refOrgId || bfutil.DefOrgRid,
          refDevId: data.refDevId || bfutil.DefOrgRid,
          lastModifyTime: bfTime.nowUtcTime(),
        }

        const oldData = bfglob.gshortNo.get(data.rid)

        return bfproto
          .sendMessage(up_db_cmd, msgObj, 'db_phone_short_no', getDbSubject())
          .then(rpc_cmd_obj => {
            bfglob.console.log('update db_phone_short_no res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.upSuccess'), 'success')
              bfglob.emit('update_global_db_phone_short_no', msgObj)

              // 添加日志
              let targetStr = msgObj.shortNo
              if (oldData.shortNo) {
                targetStr = oldData.shortNo + ' / ' + targetStr
              }
              const note = this.$t('dialog.update') + targetStr + this.$t('dialog.shortNumber')
              bfglob.emit('addnote', note)
            } else {
              this.showAddErrorMsg()
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('update short number timeout:', err)
            this.showUpdateErrorMsg()
            return Promise.resolve(false)
          })
      },
      delete_phone_short_no_data(data, del_db_cmd) {
        const msgObj = {
          ...data,
          refOrgId: data.refOrgId || bfutil.DefOrgRid,
          refDevId: data.refDevId || bfutil.DefOrgRid,
          lastModifyTime: bfTime.nowUtcTime(),
        }

        return bfproto
          .sendMessage(del_db_cmd, msgObj, 'db_phone_short_no', getDbSubject())
          .then(rpc_cmd_obj => {
            bfglob.console.log('delete db_phone_short_no res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.delSuccess'), 'success')
              bfglob.emit('delete_global_db_phone_short_no', msgObj)

              // 添加查询日志
              const note = this.$t('dialog.delete') + msgObj.shortNo + this.$t('dialog.shortNumber')
              bfglob.emit('addnote', note)
            } else {
              this.showDeleteErrorMsg()
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('delete db_short_number timeout:', err)
            this.showDeleteErrorMsg()
            return Promise.resolve(false)
          })
      },
      clearValidate(ref = '', prop = []) {
        this.$nextTick(() => {
          const targetForm = this.$refs[ref]
          targetForm && targetForm.clearValidate(prop)
        })
      },
      // 同步dataTable数据
      add_global_short_no(_data) {
        this.dataTable.body = bfutil.objToArray(bfglob.gshortNo.getAll()).sort((a, b) => {
          return bfutil.sortByProps(a, b, { noPermission: 'asc' })
        })
      },
      update_global_short_no(_data) {
        this.dataTable.body = bfutil.objToArray(bfglob.gshortNo.getAll()).sort((a, b) => {
          return bfutil.sortByProps(a, b, { noPermission: 'asc' })
        })
      },
      delete_global_short_no(_data) {
        this.dataTable.body = bfutil.objToArray(bfglob.gshortNo.getAll()).sort((a, b) => {
          return bfutil.sortByProps(a, b, { noPermission: 'asc' })
        })
      },
    },
    computed: {
      dlgTitle() {
        return this.$t('dialog.shortNumberMapping')
      },
      formLabelWidth() {
        return convertPxToRem(calcScaleSize(100)) + 'rem'
      },
      dthead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '100px',
          },
          {
            title: this.$t('dialog.shortNumber'),
            data: 'shortNo',
            width: this.isFR ? '120px' : '100px',
          },
          {
            title: this.$t('dialog.useUnit'),
            data: 'refOrgName',
            width: this.isFR ? '120px' : '100px',
          },
          {
            title: this.$t('dialog.useDevice'),
            data: 'refDevName',
            width: this.isFR ? '140px' : '100px',
          },
          {
            title: this.$t('dialog.notes'),
            data: 'note',
            width: '120px',
          },
        ]
      },
      refDevIdList() {
        return Object.keys(this.deviceDataList)
          .filter(key => {
            return this.deviceDataList[key].deviceType !== 3
          })
          .map(key => {
            return this.deviceDataList[key]
          })
      },
      refOrgIdList() {
        const list = [
          {
            label: this.$t('dialog.virOrg'),
            options: Object.keys(this.virOrgDataList).map(key => {
              return this.virOrgDataList[key]
            }),
          },
          {
            label: this.$t('dialog.realOrg'),
            options: Object.keys(this.orgDataList).map(key => {
              return this.orgDataList[key]
            }),
          },
        ]

        return list
      },
    },
    mounted() {
      // 监听数据变化同步datatable数据源
      bfglob.on('add_global_short_no', this.add_global_short_no)
      bfglob.on('update_global_short_no', this.update_global_short_no)
      bfglob.on('delete_global_short_no', this.delete_global_short_no)
    },
    components: {
      DataFormEditor,
      BfInput,
      BfSelect,
    },
  }
</script>

<style lang="scss">
  /* 统一 label 高度 */
  .el-form-item__label {
    height: 50px;
    line-height: 50px;
    display: flex;
    align-items: center;
  }
</style>
