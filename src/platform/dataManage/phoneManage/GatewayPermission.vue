<template>
  <data-form-editor
    ref="formEditor"
    class="page-gateway-permission"
    :title="dlgTitle"
    :tableName="dataTable.name"
    :data="dataTable.body"
    :column="dthead"
    :getNewData="getNewData"
    :beforeAction="beforeAction"
    :getFormRef="() => $refs.elFormRef"
    @row-delete="onDelete"
    @row-update="onUpdate"
    @row-new="onNew"
  >
    <template #form="{ formData }">
      <el-form
        ref="elFormRef"
        :model="formData"
        :label-width="formLabelWidth"
        :rules="getValidateRules(formData)"
        :validate-on-rule-change="false"
        class="grid grid-cols-1"
      >
        <el-form-item :label="$t('dialog.name')" prop="name">
          <BfInput v-model="formData.name" :maxlength="16" class="h-[50px] w-full" />
        </el-form-item>
        <el-form-item :label="$t('dialog.telephoneGateway')" prop="gatewayRid">
          <BfSelect
            v-model="formData.gatewayRid"
            :placeholder="$t('dialog.select')"
            filterable
            clearable
            :no-match-text="$t('dialog.noMatchText')"
            class="h-[50px] w-full"
          >
            <el-option v-for="(item, index) in gatewayList" :key="index" :label="item.label" :value="item.rid" />
          </BfSelect>
        </el-form-item>
        <el-form-item :label="$t('dialog.useUnit')" prop="permOrgId">
          <BfSelect
            v-model="formData.permOrgId"
            :placeholder="$t('dialog.select')"
            filterable
            clearable
            :disabled="disabledPermOrgId(formData)"
            :no-match-text="$t('dialog.noMatchText')"
            @change="clearValidate('elFormRef', ['permOrgId', 'permDevId'])"
            class="h-[50px] w-full"
          >
            <el-option v-for="(item, index) in permOrgIdList" :key="index" :disabled="item.disable" :label="item.label" :value="item.rid" />
          </BfSelect>
        </el-form-item>
        <el-form-item :label="$t('dialog.useDevice')" prop="permDevId">
          <BfSelect
            v-model="formData.permDevId"
            :placeholder="$t('dialog.select')"
            filterable
            clearable
            :disabled="disabledPermDevId(formData)"
            :no-match-text="$t('dialog.noMatchText')"
            @change="clearValidate('elFormRef', ['permOrgId', 'permDevId'])"
            class="h-[50px] w-full"
          >
            <el-option v-for="(item, index) in permDevIdList" :key="index" :disabled="item.disable" :label="item.label" :value="item.rid" />
          </BfSelect>
        </el-form-item>
        <el-form-item :label="$t('dialog.notes')" prop="note">
          <BfInput v-model="formData.note" type="textarea" resize="none" :rows="3" :maxlength="128" class="w-full" />
        </el-form-item>
      </el-form>
    </template>
  </data-form-editor>
</template>

<script>
  import bfproto from '@/modules/protocol'
  import dbCmd from '@/modules/protocol/db.pb.cmd'
  import bfutil, { getDbSubject, DeviceTypes } from '@/utils/bfutil'

  import bfNotify from '@/utils/notify'
  import bfTime from '@/utils/time'
  import validateRules from '@/utils/validateRules'
  import vueMixin from '@/utils/vueMixin'
  import { v1 as uuid } from 'uuid'
  import DataFormEditor from '@/components/common/DataFormEditor.vue'
  import { cloneDeep } from 'lodash'
  import BfInput from '@/components/bfInput/main'
  import BfSelect from '@/components/bfSelect/main'
  import { convertPxToRem, calcScaleSize } from '@/utils/setRem'

  const defaultData = {
    rid: '',
    orgId: '',
    name: '',
    mapRid: '',
    permOrgId: '',
    permDevId: '',
    gatewayRid: '',
    note: '',
  }

  export default {
    name: 'GatewayPermission',
    mixins: [vueMixin],
    data() {
      return {
        dataTable: {
          body: bfutil.objToArray(bfglob.gatewayPermission.getAll()),
          name: 'gatewayPermissionTable',
        },
        orgDataList: bfglob.gorgData.getList(),
        noPermOrgDataList: bfglob.noPermOrgData.getList(),
        deviceDataList: bfglob.gdevices.getList(),
        noPermDevDataList: bfglob.noPermDevData.getList(),
      }
    },
    methods: {
      /**
       * 编辑数据前置执行方法，允许拒绝编辑或添加新数据
       * @param {number} status Add: 1, Edit: 2, Delete: 3
       * @param {Record<string, any>?} row
       * @returns {Promise<boolean>}
       */
      beforeAction(status, row) {
        if (bfutil.notEditDataPermission()) {
          return Promise.reject('No permission')
        }
        //编辑操作,“使用单位”或“使用设备”值为默认值时，替换为空字符串显示
        if (status === 2 && row.permOrgId === bfutil.DefOrgRid) {
          this.$refs.formEditor.formData.permOrgId = ''
        }
        if (status === 2 && row.permDevId === bfutil.DefOrgRid) {
          this.$refs.formEditor.formData.permDevId = ''
        }
        return Promise.resolve(true)
      },
      disabledPermOrgId(data) {
        return !!data.permDevId && data.permDevId !== bfutil.DefOrgRid
      },
      disabledPermDevId(data) {
        return !!data.permOrgId && data.permOrgId !== bfutil.DefOrgRid
      },
      getValidateRules(data) {
        const permOrgIdRules = [validateRules.required('blur')]
        const permDevIdRules = [validateRules.required('blur')]
        return {
          name: [validateRules.required()],
          gatewayRid: [validateRules.required()],
          permOrgId: data.permDevId ? [] : permOrgIdRules,
          permDevId: data.permOrgId ? [] : permDevIdRules,
        }
      },
      async onDelete(row) {
        await this.delete_phone_gateway_permission_data(row, dbCmd.DB_PHONE_GATEWAY_PERMISSION_DELETE)
      },
      async onUpdate(row, done) {
        const isOk = await this.update_phone_gateway_permission_data(row, dbCmd.DB_PHONE_GATEWAY_PERMISSION_UPDATE)
        if (!isOk) return
        done()
      },
      // addNewCb：存在这个回调函数则需要继续添加新的一行
      async onNew(row, done, addNewCb) {
        const isOk = await this.add_phone_gateway_permission_data(row, dbCmd.DB_PHONE_GATEWAY_PERMISSION_INSERT)
        if (!isOk) return
        if (addNewCb) {
          // 重置标签页数据
          const __data = this.getNewData()
          __data.gatewayRid = row.gatewayRid
          bfutil.resetForm(this, 'elFormRef')
          addNewCb(__data)
          return
        }
        done()
      },
      // 返回一个新的默认参数对象
      getNewData() {
        return cloneDeep(defaultData)
      },
      // 数据功能操作
      add_phone_gateway_permission_data(data, add_cmd) {
        const msgObj = {
          ...data,
          rid: uuid(),
          orgId: bfutil.getBaseDataOrgId() || data.orgId || bfutil.DefOrgRid,
          lastModifyTime: bfTime.nowUtcTime(),
          permOrgId: data.permOrgId || bfutil.DefOrgRid,
          permDevId: data.permDevId || bfutil.DefOrgRid,
          setting: '{}',
        }

        return bfproto
          .sendMessage(add_cmd, msgObj, 'db_phone_gateway_permission', getDbSubject())
          .then(rpc_cmd_obj => {
            bfglob.console.log('add db_phone_gateway_permission res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              this.showAddSuccessMsg()
              bfglob.emit('add_global_db_phone_gateway_permission', msgObj)

              // 添加日志
              const note = this.$t('dialog.add') + msgObj.name + this.$t('dialog.gatewayPermission')
              bfglob.emit('addnote', note)
            } else {
              if (rpc_cmd_obj.resInfo.includes('db_phone_gateway_permission_name_key')) {
                bfNotify.messageBox(this.$t('msgbox.duplicateName'), 'error')
                return Promise.resolve(false)
              }
              this.showDbOperateErrorMsg(this.$t('msgbox.addError'), 'error', rpc_cmd_obj.resInfo)
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('add db_phone_gateway_permission timeout:', err)
            this.showAddErrorMsg()
            return Promise.resolve(false)
          })
      },
      update_phone_gateway_permission_data(data, up_cmd) {
        const msgObj = {
          ...data,
          orgId: data.orgId || bfutil.DefOrgRid,
          lastModifyTime: bfTime.nowUtcTime(),
          permOrgId: data.permOrgId || bfutil.DefOrgRid,
          permDevId: data.permDevId || bfutil.DefOrgRid,
          setting: data.setting || '{}',
        }
        const oldData = bfglob.gatewayPermission.get(data.rid)

        return bfproto
          .sendMessage(up_cmd, msgObj, 'db_phone_gateway_permission', getDbSubject())
          .then(rpc_cmd_obj => {
            // bfglob.console.log('update db_phone_gateway_permission res:', rpc_cmd_obj);
            if (rpc_cmd_obj.resInfo === '+OK') {
              this.showUpdateSuccessMsg()
              bfglob.emit('update_global_db_phone_gateway_permission', msgObj)

              // 添加日志
              let targetStr = msgObj.name
              if (oldData.name) {
                targetStr = oldData.name + ' / ' + targetStr
              }
              const note = this.$t('dialog.update') + targetStr + this.$t('dialog.gatewayPermission')
              bfglob.emit('addnote', note)
            } else {
              this.showUpdateErrorMsg()
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('update db_phone_gateway_permission timeout:', err)
            this.showUpdateErrorMsg()
            return Promise.resolve(false)
          })
      },
      delete_phone_gateway_permission_data(data, del_cmd) {
        const msgObj = {
          ...data,
          lastModifyTime: bfTime.nowUtcTime(),
          permOrgId: data.permOrgId || bfutil.DefOrgRid,
          permDevId: data.permDevId || bfutil.DefOrgRid,
        }

        bfproto
          .sendMessage(del_cmd, msgObj, 'db_phone_gateway_permission', getDbSubject())
          .then(rpc_cmd_obj => {
            // bfglob.console.log('delete db_phone_gateway_permission res:', rpc_cmd_obj);
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.delSuccess'), 'success')
              bfglob.emit('delete_global_db_phone_gateway_permission', msgObj)

              // 添加查询日志
              const note = this.$t('dialog.delete') + msgObj.name + this.$t('dialog.gatewayPermission')
              bfglob.emit('addnote', note)
            } else {
              this.showDeleteErrorMsg()
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('delete db_phone_gateway_permission timeout:', err)
            this.showDeleteErrorMsg()
            return Promise.resolve(false)
          })
      },
      clearValidate(ref = '', prop = []) {
        this.$nextTick(() => {
          const targetForm = this.$refs[ref]
          targetForm && targetForm.clearValidate(prop)
        })
      },

      // 监听数据操作变化，以同步dataTable数据源
      add_global_phone_gateway_permission(_data) {
        this.dataTable.body = bfutil.objToArray(bfglob.gatewayPermission.getAll())
      },
      update_global_phone_gateway_permission(_data) {
        this.dataTable.body = bfutil.objToArray(bfglob.gatewayPermission.getAll())
      },
      delete_global_phone_gateway_permission(_data) {
        this.dataTable.body = bfutil.objToArray(bfglob.gatewayPermission.getAll())
      },
    },
    computed: {
      dlgTitle() {
        return this.$t('dialog.gatewayPermission')
      },
      formLabelWidth() {
        return convertPxToRem(calcScaleSize(100)) + 'rem'
      },
      dthead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '120px',
          },
          {
            title: this.$t('dialog.name'),
            data: 'name',
            width: '120px',
          },
          {
            title: this.$t('dialog.telephoneGateway'),
            data: 'gatewayName',
            width: this.isFR ? '140px' : '120px',
          },
          {
            title: this.$t('dialog.useUnit'),
            data: 'permOrgName',
            width: this.isFR || this.isEN ? '150px' : '120px',
          },
          {
            title: this.$t('dialog.useDevice'),
            data: 'permDevName',
            width: this.isFR || this.isEN ? '150px' : '120px',
          },
          {
            title: this.$t('dialog.notes'),
            data: 'note',
            width: '150px',
          },
        ]
      },
      supportedDeviceTypes() {
        return [DeviceTypes.PhoneRepeater, DeviceTypes.SipGatewayDevice]
      },
      gatewayList() {
        // 网关设备列表 db_device(rid)
        return Object.keys(this.deviceDataList)
          .filter(key => {
            return this.supportedDeviceTypes.includes(this.deviceDataList[key].deviceType)
          })
          .map(key => {
            return this.deviceDataList[key]
          })
      },
      permDevIdList() {
        Object.keys(this.noPermDevDataList).forEach(key => (this.noPermDevDataList[key].disable = true))
        const originList = {
          ...this.deviceDataList,
          ...this.noPermDevDataList,
        }
        return Object.keys(originList)
          .filter(key => {
            return originList[key].deviceType !== 3
          })
          .map(key => {
            return originList[key]
          })
      },
      permOrgIdList() {
        Object.keys(this.noPermOrgDataList).forEach(key => (this.noPermOrgDataList[key].disable = true))
        return { ...this.orgDataList, ...this.noPermOrgDataList }
      },
    },
    mounted() {
      bfglob.on('add_global_phone_gateway_permission', this.add_global_phone_gateway_permission)
      bfglob.on('update_global_phone_gateway_permission', this.update_global_phone_gateway_permission)
      bfglob.on('delete_global_phone_gateway_permission', this.delete_global_phone_gateway_permission)
    },
    components: {
      DataFormEditor,
      BfInput,
      BfSelect,
    },
  }
</script>

<style lang="scss">
  /* 统一 label 高度 */
  .el-form-item__label {
    height: 50px;
    line-height: 50px;
    display: flex;
    align-items: center;
  }
</style>
