<template>
  <DataTableRowItem :iconFont="iconFont" :iconSrc="iconSrc" :disabled="!enable" @click="onClick" class="cursor-text">
    <input
      ref="inputElementRef"
      class="pointer-events-none focus:outline-none focus:ring-0 focus:opacity-70 w-[123px] flex-auto"
      :id="id"
      :placeholder="placeholder"
      :type="type"
      :value="inputModel"
      @input="onInput"
    />
  </DataTableRowItem>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  const {
    iconFont = '',
    iconSrc = '',
    id = 'data-table-row-input-' + Math.random().toString(16).slice(2, 12),
    placeholder = '',
    type = 'text',
    action,
    enable = true,
  } = defineProps<{
    iconFont?: string
    iconSrc?: string
    id?: string
    type?: string
    placeholder?: string
    action?: () => void
    enable?: boolean
  }>()

  const inputElementRef = ref<HTMLInputElement | null>(null)

  const onClick = () => {
    inputElementRef.value?.focus()
  }

  const inputModel = defineModel<string>({
    default: '',
    type: String,
  })

  const onInput = (event: Event) => {
    const target = event.target as HTMLInputElement
    inputModel.value = target.value
    action?.()
  }
</script>

<style scoped></style>
