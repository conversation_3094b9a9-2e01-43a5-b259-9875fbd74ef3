<template>
  <historyCommon
    ref="hisCom"
    :dbListName="queryProps.dbListName"
    :cmd="queryProps.cmd"
    :deviceDmrId="deviceDmrId"
    :userRid="userRid"
    :head="dthead"
    :name="dataTableName"
    :exportNamePrefix="dlgTitle"
    :parse-request-data="parseRequestdata"
    @remove-data-table-data="removeDataTableData"
  >
    <template #optionsFormItem>
      <el-form-item :label="$t(`${'dialog.terminalName'}`)" prop="deviceRid">
        <DataTableElSelect v-model="deviceDmrId" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
          <el-option v-for="item in deviceRids" :key="item.rid" :label="item.label" :value="item.dmrId" />
        </DataTableElSelect>
      </el-form-item>
      <el-form-item :label="$t('dialog.userName')" prop="userRid">
        <DataTableElSelect v-model="userRid" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
          <el-option v-for="item in userRids" :key="item.rid" :label="item.label" :value="item.rid" />
        </DataTableElSelect>
      </el-form-item>
    </template>
  </historyCommon>
</template>

<script>
  import bfutil from '@/utils/bfutil'

  import vueMixin from '@/utils/vueMixin'
  import historyCommon from '@/components/common/historyCommon.vue'

  export default {
    mixins: [vueMixin],
    data() {
      return {
        queryProps: {
          dbListName: 'db_call_dispatch_history_list',
          cmd: 53,
          isRid: false,
        },
        dataTableName: 'dispatchHistoryTable',
        deviceDmrId: '',
        userRid: '',
        deviceRids: bfglob.gdevices.getList(),
        userRids: bfglob.guserData.getList(),
      }
    },
    methods: {
      removeDataTableData() {
        this.deviceDmrId = ''
        this.userRid = ''
      },
      parseRequestdata(item) {
        var userName = bfglob.guserData.getUserNameByKey(item.personId)
        item._targetChannel = item.targetChannel
        if (item.deviceId === '00000000') {
          // cb21切换信道历史
          item.orgShortName = ''
          item.sponsor = userName ? this.$t('dataTable.sysCenter') + '/' + userName : this.$t('dataTable.sysCenter')
          var targetArr = item.dispatchTargetDmrid.split(';')
          var sponsorArr = []
          var groudArr = targetArr[0].split(',')
          var deviceArr = targetArr[1].split(',')
          if (groudArr.length === 1) {
            var oneOrg = bfglob.gorgData.getDataByIndex(groudArr[0])
            item.orgShortName = oneOrg ? bfglob.gorgData.getOrgNameByKey(oneOrg.parentOrgId) : ''
            if (oneOrg) {
              sponsorArr.push(oneOrg.orgShortName)
            }
          } else {
            for (var j in groudArr) {
              // 目标为单位
              var orgItem = bfglob.gorgData.getDataByIndex(groudArr[j])
              if (orgItem) {
                sponsorArr.push(orgItem.orgShortName)
              }
            }
          }
          if (deviceArr.length === 1) {
            var oneDev = bfglob.gdevices.getDataByIndex(deviceArr[0])
            if (oneDev) {
              var oneDevName = oneDev.selfId
              sponsorArr.push(oneDevName)
            } else {
              sponsorArr.push(deviceArr[0])
            }
          } else {
            for (var k in deviceArr) {
              // 目标为对讲机
              var devItem = bfglob.gdevices.getDataByIndex(deviceArr[k])
              if (devItem) {
                var devItemName = devItem.selfId
                sponsorArr.push(devItemName)
              } else {
                sponsorArr.push(deviceArr[k])
              }
            }
          }
          item.dispatchTarget = sponsorArr.filter(v => !!v).join(',')
        } else {
          // bc11调度指令
          var device = bfglob.gdevices.getDataByIndex(item.deviceId)
          item.orgShortName = bfglob.gorgData.getOrgNameByKey(device.orgId)
          item.sponsor = userName ? device.selfId + '/' + userName : device.selfId
          var targetAddr = bfglob.dmrAddr.get(item.dispatchTargetDmrid)
          item.dispatchTarget = ''
          if (item.dispatchCode === 2 || item.dispatchCode === 0) {
            item._targetChannel = ''
          }
          if (targetAddr) {
            if (targetAddr.type === 1) {
              const key = targetAddr.title
              item.dispatchTarget = this.$t(`dataTable.${key}`)
            } else {
              item.dispatchTarget = targetAddr.baseNo + this.$t('dialog.baseStation') + this.$t('dataTable.allChannel')
            }
          }
        }

        return item
      },
      get_dispatch_type_content(dispatchCode, dispatchType) {
        const typeCode = dispatchType.toString(16)
        const typeCodeName = bfutil.get_schedule_model(typeCode)
        let typeName = ''
        switch (dispatchCode) {
          case 1:
          case 2:
            typeName = this.$t('dialog.starting') + ':' + typeCodeName
            break
          case 0:
            typeName = this.$t('dialog.cancel') + ':' + typeCodeName
            break
          case 110:
            typeName = this.$t('dialog.cancelCenterCHCtrl')
            break
          case 111:
            typeName = this.$t('dialog.openCenterCHCtrl')
            break
          default:
            typeName = typeCodeName
        }
        return typeName
      },
    },
    components: {
      historyCommon,
    },
    computed: {
      dthead() {
        return [
          {
            title: this.$t('dataTable.dispatchTime'),
            data: 'actionTime',
            width: this.isFR ? '140px' : '120px',
          },
          {
            title: this.$t('dataTable.sponsor'),
            data: 'sponsor',
            width: '100px',
          },
          {
            title: this.$t('dataTable.dispatchTarget'),
            data: 'dispatchTarget',
            width: this.isFR || this.isEN ? '140px' : '100px',
          },
          {
            title: this.$t('dataTable.dispatchTypeName'),
            data: null,
            width: this.isFR ? '140px' : '120px',
            render: (data, type, row, meta) => {
              return this.get_dispatch_type_content(data.dispatchCode, data.dispatchType)
            },
          },
          {
            title: this.$t('dataTable.targetChannel'),
            data: '_targetChannel',
            width: this.isFR ? '100px' : this.isEN ? '120px' : '80px',
          },
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '100px',
          },
        ]
      },
      dlgTitle() {
        return this.$t('dialog.dispatch')
      },
    },
  }
</script>

<style></style>
