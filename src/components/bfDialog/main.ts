import { defineComponent, h, computed, ref } from 'vue'
import { ElDialog } from 'element-plus'
// 确保 ElDialog 样式被导入
import 'element-plus/es/components/dialog/style/css'
import './main.scss'

export default defineComponent({
  name: 'BfDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    // 将 ElDialog 的属性作为组件的 props
    ...ElDialog.props,
  },
  emits: ['update:modelValue'],
  setup(props: InstanceType<typeof ElDialog>['$props'], { emit, slots, expose, attrs }) {
    const visible = computed({
      get() {
        return props.modelValue
      },
      set(value) {
        emit('update:modelValue', value)
      },
    })
    const CloseIcon = h('span', {
      class: 'w-full h-full close-icon',
    })

    // 构建传递给 ElDialog 的属性对象
    const dialogProps = computed(() => {
      return {
        ...attrs,
        ...props,
        closeIcon: props.closeIcon ?? CloseIcon,
        destroyOnClose: props.destroyOnClose ?? true,
        class: ['bf-dialog', attrs.class].filter(Boolean).join(' '),
        modelValue: visible.value,
        'onUpdate:modelValue': (val: boolean) => {
          visible.value = val
        },
      }
    })

    // 向父组件暴露 dialogRef
    const dialogRef = ref<InstanceType<typeof ElDialog>>()
    expose({
      dialogRef,
    })

    // 使用 h 函数渲染 ElDialog
    return () =>
      h(
        ElDialog,
        {
          ...dialogProps.value,
          ref: dialogRef,
        },
        slots
      )
  },
})
