@use '@/css/common.scss';

.el-select.bf-select {
  @extend .bf-component-size;
  --el-select-border-radius: 0;
  --el-select-bg-color: transparent;
  --el-select-text-color: #fff;
  --el-select-border-color: rgba(148, 204, 232, 1);

  .el-select__wrapper {
    background-color: transparent;
    border-radius: var(--el-select-border-radius);
    box-shadow: 0 0 0 2px var(--el-select-border-color, var(--el-border-color)) inset;
    height: 100%;
    .el-select__selection {
      .el-select__selected-item.el-select__placeholder {
        color: var(--el-select-text-color);
      }
      input {
        color: var(--el-select-text-color);
      }
    }
  }
}

.el-popper.bf-select-popper {
  --el-popper-border-radius: 10px;
  --el-popper-bg-color: #012C4A;
  --el-select-tree-hover-bg-color: rgba(6, 121, 204, 0.5);
  --el-select-popper-border-color: #50D5FF;
  --el-select-tree-expand-border-radius: 3px;
  --el-select-hover-border-radius: 2px;
  --el-select-item-hover-bg-color: #01375C;
  --el-select-tree-expand-bg-color: var(--el-select-item-hover-bg-color);

  background-color: var(--el-popper-bg-color);
  border: 2px solid transparent;
  border-radius: var(--el-popper-border-radius); 
  background-image: linear-gradient(var(--el-popper-bg-color), var(--el-popper-bg-color)), linear-gradient(to bottom, rgba(80, 213, 255, 0.63), rgba(80, 213, 255, 0));
  background-clip: padding-box, border-box;
  background-origin: border-box;
  background-color: transparent;
  .el-popper__arrow::before {
    background-color: var(--el-popper-bg-color);
    border: 2px solid var(--el-select-popper-border-color);
  }

  .el-scrollbar {
      // 树形结构选择器特有样式
    .el-tree {
      &>.el-tree-node {
        margin: 2px 0;
        padding: 2px 10px;
      }
      padding: 0 20px;
      .el-tree-node:focus>.el-tree-node__content,
      .el-tree-node.is-current>.el-tree-node__content{ 
        background-color: var(--el-select-tree-hover-bg-color);
        border-radius: var(--el-select-hover-border-radius);
      }
      .el-tree-node.is-expanded {
        &>.el-tree-node__content, .el-tree-node__children {
          margin: 2px 0;
        }
        background-color: var(--el-select-tree-expand-bg-color);
        border-radius: var(--el-select-tree-expand-border-radius);
      }
      .el-tree-node {
        .el-tree-node__content:hover, .el-tree-node__children:only-child:hover {
          background-color: var(--el-select-tree-hover-bg-color);
          border-radius: var(--el-select-hover-border-radius);
        }
      }
    }

    // 普通选择器样式
    .el-select-dropdown__wrap {
      .el-select-dropdown__item {
        margin: 0 10px;
        &.is-hovering {
          background-color: var(--el-select-item-hover-bg-color);
        }
        // &.is-selected {
        //   color: #fff;
        // }
      }
    }
  }
}