@use '@/components/bfInput/main.scss';

.el-input-number.bf-input-number {
  --el-border-radius-base: 0;
  --el-fill-color-light: transparent;

  .el-input-number__decrease {
    border-right: none;
  }

  .el-input-number__increase {
    border-left: none;
  }

  .el-input-number__decrease:hover ~ .el-input:not(.is-disabled) .el-input__wrapper:not(.is-focus),
  .el-input-number__increase:hover ~ .el-input:not(.is-disabled) .el-input__wrapper:not(.is-focus) {
    --el-input-focus-border-color: var(--el-input-hover-border-color);
    box-shadow: 0 0 0 var(--bf-border-size) var(--el-input-focus-border-color, var(--el-color-primary)) inset;
  }

  .el-input {
    @extend .bf-input;

    .el-input__wrapper {
      height: 100%;
    }
  }
}
