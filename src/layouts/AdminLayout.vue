<template>
  <el-container class="common-bg w-full h-full !flex-col app-layout">
    <!-- 顶部导航 -->
    <BGHead class="flex-none" />

    <!-- 路由页面 -->
    <el-container class="flex-auto gap-[45px] max-h-[100vh_-_146.5px] overflow-y-auto">
      <bf-data-nav class="w-[292px]" />

      <el-main class="page-container !pr-[38px] !pb-[47px] !p-0 !flex flex-col gap-[5px]">
        <page-header>
          <div v-if="childRoutes.length > 0" :key="route.matched[1]?.name" class="flex items-center gap-4 z-[100] ml-[50px]">
            <el-tag
              v-for="child in childRoutes"
              :key="child.name"
              class="!border-none text-white text-[14px] font-medium cursor-pointer"
              :style="getTagStyle(child)"
              @click="handleChildRoute<PERSON>lick(child)"
            >
              {{ getChildRoute<PERSON>abel(child) }}
            </el-tag>
          </div>
        </page-header>

        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <!-- 使用 keep-alive 缓存路由组件，确保组件状态保持 -->
            <keep-alive>
              <div class="w-full h-full">
                <component :is="Component" />
              </div>
            </keep-alive>
          </transition>
        </router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
  import '@/modules/dataManager'
  import BGHead from '@/layouts/BFHead.vue'
  import BfDataNav from '@/layouts/BfDataNav.vue'
  import PageHeader from '@/layouts/PageHeader.vue'
  import bfprocess from '@/utils/bfprocess.js'
  import { useRoute, useRouter } from 'vue-router'
  import { computed, ref, h, createApp } from 'vue'
  import { useI18n } from 'vue-i18n'
  import i18n from '@/modules/i18n'
  import authorization from '@/platform/dataManage/otherManage/authorization.vue'
  import systemVersion from '@/platform/dataManage/otherManage/systemVersion.vue'
  import bfDialog from '@/components/bfDialog/main'

  const route = useRoute()
  const router = useRouter()
  const { t } = useI18n()
  bfprocess.loginedAfterFunc()

  // 获取当前路由的子路由
  const childRoutes = computed(() => {
    // 获取当前二级路由名称（如 controllerManage, deviceManage 等）
    const currentParentRoute = route.matched[1]?.name
    if (!currentParentRoute) return []

    // 从路由配置中获取子路由
    const parentRouteConfig = router
      .getRoutes()
      .find(r => r.path === '/dataManage')
      ?.children?.find(child => child.name === currentParentRoute)
    console.log('parentRouteConfig?.children ', parentRouteConfig?.children)
    return parentRouteConfig?.children || []
  })

  // 子路由名称映射
  const childRouteLabels = computed(() => ({
    // 设备管理子路由
    Controllers: t('nav.ctrlData'),
    repeaterWriteFrequency: t('nav.repeaterWriteFrequency'),
    ctrlonlineHistory: t('nav.controllerHistoryEvent'),

    // 终端管理子路由
    Devices: t('dialog.deviceDataTitle'),
    interphoneWf: t('nav.interphoneWriteFrequency'),
    onlineHistory: t('nav.switchHistory'),

    // 电话管理子路由
    GatewayFilter: t('nav.phoneBlackWhiteList'),
    GatewayPermission: t('nav.phoneDeviceAuth'),
    PredefinedPhoneBook: t('nav.predefinedPhoneBook'),
    ShortNumberMapping: t('nav.phoneGatewayMapping'),

    // 巡逻管理子路由
    LinePoint: t('nav.patrolPointManage'),
    Lines: t('nav.patrolRouteManage'),
    Rules: t('nav.patrolRuleManage'),

    // 其他操作子路由
    MapPoints: t('nav.mapPointData'),
    DynamicGroup: t('dynamicGroup.title'),
    authorization: t('nav.authorization'),
    relatedSoftware: t('nav.relatedSoftware'),
    systemVersion: t('nav.version'),
    notes: t('nav.runNotes'),
    crudHistory: t('nav.crudHistory'),
  }))

  // 获取子路由的显示标签
  const getChildRouteLabel = child => {
    return childRouteLabels.value[child.name] || child.name
  }

  // 获取标签样式
  const getTagStyle = child => {
    const isActive = route.name === child.name

    if (isActive) {
      // 激活状态的橙色渐变
      return 'background: linear-gradient(90deg, rgba(253, 161, 19, 0.0001) 0%, #fda215 50.16%, rgba(254, 159, 15, 0.0001) 100%)'
    } else {
      // 非激活状态的蓝色渐变
      return 'background: linear-gradient(90deg, rgba(20, 186, 255, 0.00006) 0%, rgba(20, 186, 255, 0.6) 50.16%, rgba(20, 186, 255, 0.00006) 100%)'
    }
  }

  // 组件映射表
  const componentMap = {
    authorization: authorization,
    systemVersion: systemVersion,
  }

  // 弹窗标题映射表
  const dialogTitleMap = {
    authorization: 'nav.authorization',
    systemVersion: 'nav.version',
  }

  /**
   * 通用弹窗打开函数
   * @param {string} componentName - 组件名称
   * @param {Object} props - 传递给组件的属性
   * @returns {Promise} 返回Promise，可以获取弹窗关闭时的结果
   */
  function openDialog(componentName, props = {}) {
    return new Promise(resolve => {
      const visible = ref(true)
      const component = componentMap[componentName]

      if (!component) {
        console.error(`Component ${componentName} not found in componentMap`)
        resolve(null)
        return
      }

      const onClose = result => {
        visible.value = false
        resolve(result)
        app.unmount()
        container.remove()
      }

      // 判断组件是否需要包装在 bfDialog 中
      // systemVersion 现在自带 bfDialog，不需要额外包装
      const needsDialog = false // componentName === 'systemVersion'

      const Wrapper = {
        setup() {
          return () => {
            if (!visible.value) return null

            if (needsDialog) {
              // 需要包装在 bfDialog 中的组件
              return h(
                bfDialog,
                {
                  modelValue: visible.value,
                  'onUpdate:modelValue': val => (visible.value = val),
                  title: t(dialogTitleMap[componentName] || 'dialog.title'),
                  closeOnClickModal: false,
                  closeOnPressEscape: false,
                  appendToBody: true,
                  center: true,
                  class: 'setting-dialog',
                  modalClass: 'dialog-modal-mask',
                  onClose,
                },
                {
                  default: () => h(component, props),
                }
              )
            } else {
              // 自带弹窗的组件（如 authorization、systemVersion）
              // 使用 dialogVisible 避免与 vueMixin 中的 visible 冲突
              return h(component, {
                ...props,
                dialogVisible: visible.value,
                'onUpdate:dialogVisible': val => (visible.value = val),
              })
            }
          }
        },
      }

      const container = document.createElement('div')
      document.body.appendChild(container)
      const app = createApp(Wrapper)

      // 为应用提供完整的国际化支持
      app.use(i18n)

      app.mount(container)
    })
  }

  // 处理子路由点击
  const handleChildRouteClick = child => {
    // 检查是否为不需要跳转的路由
    if (child.meta?.unJumpRoute) {
      // 打开对应的弹窗组件
      openDialog(child.name)
      return
    }

    // 正常路由跳转逻辑
    const currentParentRoute = route.matched[1]?.name
    if (!currentParentRoute) return

    const targetPath = `/dataManage/${currentParentRoute}/${child.name}`
    router.push({
      path: targetPath,
      query: route.query, // 保持查询参数
    })
  }
</script>

<style scoped lang="scss"></style>
