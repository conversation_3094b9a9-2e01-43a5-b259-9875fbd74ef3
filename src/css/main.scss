@use './common.scss';

body {
  font-family: 'AlibabaPuHuiTi2', sans-serif !important;
  font-size: var(--bf-base-font-size, 16px) !important;
}

.common-bg {
  background: url('@/assets/images/loginBg/1x/login_bg.webp') no-repeat center center;
  background-size: 100% 100%;
}

.bf-logo {
  background: url('@/assets/images/loginBg/1x/logo.webp') no-repeat center center;
  background-size: 100% 100%;
}

/*在2.5k屏幕以上才使用4k分辨率*/
@media screen and (min-width: 2561px) {
  .common-bg {
    background: url('@/assets/images/loginBg/2x/login_bg.webp') no-repeat center center;
    background-size: 100% 100%;
  }

  .bf-logo {
    background: url('@/assets/images/loginBg/2x/logo.webp') no-repeat center center;
    background-size: 100% 100%;
  }
}

.dialog-modal-mask {
  background-color: rgba(0, 13, 36, 0.75);
}

.el-form.bf-form {
  @extend .bf-component-size;

  .el-form-item .el-form-item__label {
    line-height: var(--el-component-size);
    height: var(--el-component-size);
  }

  &.el-form--large {
    .el-form-item .el-form-item__label {
      line-height: var(--el-component-size-large);
      height: var(--el-component-size-large);
    }
  }

  &.el-form--small {
    .el-form-item .el-form-item__label {
      line-height: var(--el-component-size-small);
      height: var(--el-component-size-small);
    }
  }
}
