{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "Node", "strict": true, "jsx": "preserve", "importHelpers": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "allowImportingTsExtensions": true, "noEmit": true, "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "skipLibCheck": true, "noImplicitAny": false, "baseUrl": "./", "types": ["vue", "node", "j<PERSON>y"], "paths": {"@/*": ["src/*"]}, "lib": ["ESNext", "DOM"]}, "include": ["*.d.ts", "src/**/*.js", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.jsx", "src/**/*.vue"], "exclude": ["node_modules", "dist"]}